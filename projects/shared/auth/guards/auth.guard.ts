import { Injectable, inject } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, catchError, switchMap } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { TokenStorageService } from '../services/token-storage.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly tokenStorage = inject(TokenStorageService);
  private readonly centralizedRedirectService = inject(CentralizedRedirectService);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();
    const loginType = this.tokenStorage.getLoginType();


    console.log('AuthGuard: Checking access for', state.url, {
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      loginType
    });

    // If we have a refresh token but no access token, try to refresh
    if (!accessToken && refreshToken) {
      console.log('AuthGuard: Attempting token refresh');
      return this.attemptTokenRefreshWithResilience(refreshToken, loginType, state.url);
    }

    // If no tokens at all, redirect to marketing login
    if (!accessToken && !refreshToken) {
      console.log('AuthGuard: No tokens found, redirecting to login');
      this.centralizedRedirectService.storeIntendedDestination(state.url);
      this.redirectToMarketingLogin(state.url, 'no_tokens');
      return of(false);
    }

    // If we have tokens, allow access
    console.log('AuthGuard: User authenticated, allowing access');
    return of(true);
  }

  /**
   * Attempt token refresh with resilience mechanisms
   */
  private attemptTokenRefreshWithResilience(
    refreshToken: string,
    loginType: 'sso' | 'basic' | null,
    targetUrl: string
  ): Observable<boolean> {
    console.log('AuthGuard: Attempting token refresh for', loginType);

    if (loginType === 'basic') {
      return this.authService.basicRefreshToken().pipe(
        map(() => {
          console.log('AuthGuard: Basic token refresh successful');
          return true;
        }),
        catchError((error) => {
          console.error('AuthGuard: Basic token refresh failed', error);
          return this.handleTokenRefreshFailure(error, loginType, targetUrl);
        })
      );
    } else {
      return this.authService.refreshToken(refreshToken).pipe(
        map(() => {
          console.log('AuthGuard: SSO token refresh successful');
          return true;
        }),
        catchError((error) => {
          console.error('AuthGuard: SSO token refresh failed', error);
          return this.handleTokenRefreshFailure(error, loginType, targetUrl);
        })
      );
    }
  }

  /**
   * Handle token refresh failures with appropriate logout and redirect
   */
  private handleTokenRefreshFailure(
    error: any,
    loginType: 'sso' | 'basic' | null,
    targetUrl: string
  ): Observable<boolean> {
    const errorType = this.categorizeAuthError(error);

    console.error('AuthGuard: Handling token refresh failure', { errorType, loginType, error });

    // For SSO, attempt proper logout to clear SSO session
    if (loginType === 'sso') {
      return this.performSSOLogoutAndRedirect(targetUrl, errorType);
    }

    // For basic auth or unknown, just clear tokens and redirect
    return this.handleAuthenticationFailure(targetUrl, errorType);
  }

  /**
   * Perform SSO logout to clear SSO session before redirecting to login
   */
  private performSSOLogoutAndRedirect(targetUrl: string, errorType: string): Observable<boolean> {
    console.log('AuthGuard: Performing SSO logout due to auth failure');

    return this.authService.logout().pipe(
      catchError((logoutError) => {
        console.warn('AuthGuard: SSO logout failed, proceeding with token cleanup', logoutError);
        // Even if logout fails, clear local tokens
        this.tokenStorage.clearTokens();
        return of(null);
      }),
      switchMap(() => {
        return this.handleAuthenticationFailure(targetUrl, errorType);
      })
    );
  }

  /**
   * Handle authentication failure with proper cleanup and redirect
   */
  private handleAuthenticationFailure(targetUrl: string, errorType: string): Observable<boolean> {
    // Store intended destination for post-login redirect
    this.centralizedRedirectService.storeIntendedDestination(targetUrl);

    // Clear any remaining tokens
    this.tokenStorage.clearTokens();

    // Redirect to marketing login with error context
    this.redirectToMarketingLogin(targetUrl, errorType);

    return of(false);
  }

  /**
   * Categorize authentication errors for better handling
   */
  private categorizeAuthError(error: any): string {
    if (!error) return 'unknown_error';

    if (error.name === 'TimeoutError') return 'timeout';
    if (error.status === 401) return 'unauthorized';
    if (error.status === 403) return 'forbidden';
    if (error.status === 429) return 'rate_limited';
    if (error.status >= 500) return 'server_error';
    if (error.status === 0) return 'network_error';

    return 'unknown_error';
  }

  private redirectToMarketingLogin(returnUrl: string, errorType?: string): void {
    // Use centralized redirect service to handle env-specific login URL and storage
    console.log('AuthGuard: Redirecting to marketing login via CentralizedRedirectService', { returnUrl, errorType });
    this.centralizedRedirectService.redirectToMarketingLogin(returnUrl);
  }
}
