import { Component, OnInit, signal, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from '@angular/forms';
import { Router } from '@angular/router';

import { HttpErrorResponse } from '@angular/common/http';
import logintext from './login.json';
import {
  AvaTextboxComponent,
  IconComponent,
  ButtonComponent,
  PopupComponent,
} from '@ava/play-comp-library';
import { AuthService } from '@shared/auth/services/auth.service';
import { TokenStorageService } from '../../../auth/services/token-storage.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

export interface SavedAccount {
  email: string;
  profilePic?: string;
  isSelected?: boolean;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    IconComponent,
    ButtonComponent,
    PopupComponent,
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  loginMode = signal<'sso' | 'form'>('sso');
  isLoading = signal(false);
  showPassword = signal(false);
  loginForm: FormGroup;
  errorMessage = signal<string | null>(null);
  showErrorPopup = signal(false);
  popupMessage = signal('');
  public labels: any = logintext;

  private authService = inject(AuthService);
  private tokenStorage = inject(TokenStorageService);
  private centralizedRedirectService = inject(CentralizedRedirectService);

  constructor(
    private fb: FormBuilder,
    private router: Router,
  ) {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', Validators.required],
      keepSignedIn: [false],
    });
  }

  ngOnInit(): void {
    // Check for SSO errors from callback
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras?.state) {
      const state = navigation.extras.state as any;
      if (state.error && state.errorType === 'sso_error') {
        console.log('SSO error detected:', state.error);
        this.popupMessage.set(state.error);
        this.showErrorPopup.set(true);
      }
    }

    // Check if user is already authenticated
    if (this.isAuthenticated()) {
      this.handlePostLoginRedirect();
      return;
    }

    const storedLoginType = this.tokenStorage.getLoginType();
    if (storedLoginType === 'sso' || storedLoginType === 'basic') {
      this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');
    } else {
      this.loginMode.set('sso');
    }
  }

  // Simple authentication check
  private isAuthenticated(): boolean {
    // Only treat as authenticated when we have a valid access token.
    // Having only a refresh token can cause redirect loops before token exchange.
    const accessToken = this.tokenStorage.getAccessToken();
    return !!accessToken;
  }

  getControl(name: string): FormControl {
    return this.loginForm.get(name) as FormControl;
  }

  onBasicLogin(): void {
    if (this.loginForm.valid) {
      // Check if AuthService is configured
      if (!this.authService.getAuthConfig()) {
        console.error('AuthService not configured. Please ensure the app is properly initialized.');
        this.isLoading.set(false);
        this.popupMessage.set('Authentication service not ready. Please refresh the page and try again.');
        this.showErrorPopup.set(true);
        return;
      }

      this.isLoading.set(true);
      this.errorMessage.set(null);

      const { username, password } = this.loginForm.value;
      this.authService.basicLoginWithCredentials(username, password).subscribe({
        next: () => {
          this.tokenStorage.storeLoginType('basic');
          this.handlePostLoginRedirect();
        },
        error: (error: HttpErrorResponse) => {
          this.isLoading.set(false);
          let errorMessage = 'Invalid username or password. Please try again.';
          
          if (error.error && typeof error.error === 'string') {
            errorMessage = error.error;
          }
          this.popupMessage.set(errorMessage);
          this.showErrorPopup.set(true);
          console.error('Login failed:', error);
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  onCompanyLogin(): void {
    // Check if AuthService is configured
    if (!this.authService.getAuthConfig()) {
      console.error('AuthService not configured. Please ensure the app is properly initialized.');
      this.popupMessage.set('Authentication service not ready. Please refresh the page and try again.');
      this.showErrorPopup.set(true);
      return;
    }

    this.isLoading.set(true);
    this.errorMessage.set(null);

    // Check for intended destination before initiating SSO
    const intendedDestination = this.getIntendedDestination();
    if (intendedDestination) {
      console.log('Found intended destination for SSO login:', intendedDestination);
    } else {
      console.log('No intended destination found, will use default redirect');
    }

    console.log('Initiating SSO login...');
    this.authService.loginSSO().subscribe({
      next: () => {
        console.log('SSO login initiated successfully');
        this.isLoading.set(false);
        // The user will be redirected to SSO provider, so we don't need to do anything else here
      },
      error: (error) => {
        console.error('SSO login failed:', error);
        this.isLoading.set(false);
        this.popupMessage.set('Failed to initiate login with company account. Please try again.');
        this.showErrorPopup.set(true);
      },
    });
  }

  /**
   * Get intended destination from storage
   */
  private getIntendedDestination(): string | null {
    // Try sessionStorage first
    let destination = sessionStorage.getItem('intendedDestination');
    
    // If not found in sessionStorage, try cookie
    if (!destination) {
      destination = this.getCookie('intendedDestination');
    }
    
    // Also check URL parameters for returnUrl
    if (!destination) {
      const urlParams = new URLSearchParams(window.location.search);
      destination = urlParams.get('returnUrl');
    }
    
    return destination;
  }

  /**
   * Get a cookie value
   */
  private getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        return decodeURIComponent(c.substring(nameEQ.length, c.length));
      }
    }
    return null;
  }

  private handlePostLoginRedirect(): void {
    // Use centralized redirect service to determine where to go
    this.centralizedRedirectService.handlePostLoginRedirect();
  }

  togglePasswordVisibility(): void {
    this.showPassword.set(!this.showPassword());
  }

  clearInput(fieldName: string): void {
    this.loginForm.get(fieldName)?.setValue('');
    this.loginForm.get(fieldName)?.markAsTouched();
  }

  clearUsername(): void {
    this.clearInput('username');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.touched && field?.errors) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  // Check if both username and password fields are filled
  areFieldsFilled(): boolean {
    const username = this.loginForm.get('username')?.value;
    const password = this.loginForm.get('password')?.value;
    return !!username && !!password && username.trim() !== '' && password.trim() !== '';
  }

  onForgotPassword(): void {
    this.router.navigate(['/forgot-password']);
  }

  onTroubleSigningIn(): void {
    this.router.navigate(['/help']);
  }
}
