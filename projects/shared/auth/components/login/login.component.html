<div id="login-container" class="row">
  <div class="col-5 p-0">
    <img class="login-image" src="login.png" alt="" />
  </div>
  <div class="col-7 p-0 login-section">
    <div class="sign-in-container">
      <div class="heading">
        <h3 class="mb-2 main-heading">{{ labels.labels.main_heading }}</h3>
        <p class="sub-heading">{{ labels.labels.sub_heading }}</p>
      </div>
      <form [formGroup]="loginForm" class="new-login-form">
        <div class="form-field-wrapper">
          <ava-textbox
            [label]="labels.labels.username"
            [placeholder]="labels.placeholders.username"
            [required]="true"
            formControlName="username"
            [error]="getFieldError('username')"
            [iconSeparator]="false"
            [iconSpacing]="'normal'"
          >
            <ava-icon
              slot="icon-end"
              iconName="x"
              [iconSize]="16"
              [cursor]="true"
              (click)="clearUsername()"
              [disabled]="false"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="form-field-wrapper mt-4 mb-4">
          <ava-textbox
            [label]="labels.labels.password"
            [type]="showPassword() ? 'text' : 'password'"
            [placeholder]="labels.placeholders.password"
            [required]="true"
            formControlName="password"
            [error]="getFieldError('password')"
            [iconSeparator]="false"
            [iconSpacing]="'normal'"
          >
            <ava-icon
              slot="icon-end"
              [iconName]="showPassword() ? 'eye-off' : 'eye'"
              [iconSize]="18"
              [cursor]="true"
              (click)="togglePasswordVisibility()"
              [disabled]="false"
            >
            </ava-icon>
          </ava-textbox>
        </div>
        <div class="new-buttons-container">
          <div class="sign-in-button mb-5">
            <ava-button
              class="mb-4"
              [label]="labels.labels.sign_in + ' ' + labels.labels.arrow"
              variant="primary"
              size="large"
              [processing]="isLoading()"
              [width]="'100%'"
              (userClick)="onBasicLogin()"
              [disabled]="!areFieldsFilled() || isLoading()"
            >
            </ava-button>
          </div>

          <div class="new-separator">
            {{ labels.labels.seperator }}
          </div>
          <div class="login-with-company mt-5">
            <ava-button
              [label]="labels.labels.login_with_company"
              variant="secondary"
              size="large"
              [width]="'100%'"
              (userClick)="onCompanyLogin()"
            >
            </ava-button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<ava-popup
  [show]="showErrorPopup()"
  title="Login Failed"
  [message]="popupMessage()"
  [showHeaderIcon]="true"
  headerIconName="alert-circle"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="false"
  [showConfirm]="true"
  [confirmButtonLabel]="'OK'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="showErrorPopup.set(false)"
  (closed)="showErrorPopup.set(false)"
>
</ava-popup>
