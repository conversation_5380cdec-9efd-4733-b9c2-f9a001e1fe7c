import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

@Component({
  selector: 'app-callback',
  template: `
    <div class="callback-container">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>Processing authentication...</p>
      </div>
    </div>
  `,
  styles: [`
    .callback-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }
    .loading-spinner {
      text-align: center;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `]
})
export class CallbackComponent implements OnInit, OnDestroy {
  private subscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private centralizedRedirectService: CentralizedRedirectService,
  ) {}

  ngOnInit(): void {
    console.log('Callback component initialized');
    console.log('Current URL:', window.location.href);
    
    // Log the current intended destination before processing
    const currentIntendedDestination = this.getCurrentIntendedDestination();
    console.log('Current intended destination before processing:', currentIntendedDestination);
    
    const refreshToken = this.route.snapshot.queryParams['refresh_token'];
    const code = this.route.snapshot.queryParams['code'];
    const error = this.route.snapshot.queryParams['error'];
    const errorDescription = this.route.snapshot.queryParams['error_description'];

    console.log('Callback parameters:', { 
      hasRefreshToken: !!refreshToken, 
      hasCode: !!code, 
      hasError: !!error,
      error,
      errorDescription
    });

    if (error) {
      console.error('SSO authentication error:', error, errorDescription);
      this.handleAuthError(error, errorDescription);
      return;
    }

    if (refreshToken) {
      console.log('Handling token refresh');
      this.handleTokenRefresh(refreshToken);
    } else if (code) {
      console.log('Handling authorization code exchange, code length:', code.length);
      this.handleCodeExchange(code);
    } else {
      console.warn('No authorization code or refresh token found in callback URL');
      this.handleAuthError('no_code', 'No authorization code or refresh token found');
    }
  }

  /**
   * Get current intended destination for debugging
   */
  private getCurrentIntendedDestination(): string | null {
    // Try sessionStorage first
    let destination = sessionStorage.getItem('intendedDestination');
    
    // If not found in sessionStorage, try cookie
    if (!destination) {
      destination = this.getCookie('intendedDestination');
    }
    
    return destination;
  }

  /**
   * Get a cookie value
   */
  private getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        return decodeURIComponent(c.substring(nameEQ.length, c.length));
      }
    }
    return null;
  }

  private handleAuthError(error: string, description?: string): void {
    const errorMessage = description || `Authentication failed: ${error}`;
    console.error('Authentication error:', errorMessage);
    
    // Navigate to login with error state
    this.router.navigate(['/login'], {
      state: {
        error: errorMessage,
        errorType: 'sso_error'
      },
    });
  }

  private handleTokenRefresh(refreshToken: string): void {
    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: () => {
        console.log('Token refresh successful');
        this.handlePostLoginRedirect();
      },
      error: (err) => {
        console.error('Token refresh failed:', err);
        this.handleAuthError('refresh_failed', 'Token refresh failed. Please login again.');
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleCodeExchange(code: string): void {
    const exchangeSub = this.authService
      .exchangeCodeForToken(code)
      .subscribe({
        next: (tokenPair) => {
          console.log('Token exchange successful');
          this.handlePostLoginRedirect();
        },
        error: (err) => {
          console.error('Token exchange failed:', err);
          this.handleAuthError('exchange_failed', 'Failed to exchange authorization code for token. Please try again.');
        },
      });
    this.subscription.add(exchangeSub);
  }

  private handlePostLoginRedirect(): void {
    console.log('Handling post-login redirect');
    try {
      // Use centralized redirect service to determine where to go
      this.centralizedRedirectService.handlePostLoginRedirect();
    } catch (error) {
      console.error('Error during post-login redirect:', error);
      // Fallback to default redirect
      window.location.href = '/';
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
