import { Injectable, inject } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpErrorResponse,
} from '@angular/common/http';
import {
  Observable,
  tap,
  BehaviorSubject,
  catchError,
  throwError,
  map,
  of,
} from 'rxjs';
import { TokenStorageService } from './token-storage.service';
import { AuthConfig } from '../interfaces/auth-config.interface';

// Organization hierarchy interfaces
export interface Team {
  teamId: number;
  teamName: string;
  default?: boolean;
}

export interface Project {
  projectId: number;
  projectName: string;
  teams: Team[];
}

export interface Domain {
  domainId: number;
  domainName: string;
  projects: Project[];
}

export interface Organization {
  orgId: number;
  organizationName: string;
  domains: Domain[];
}

interface LoginResponse {
  loginUrl: string;
}

interface TokenResponse {
  token_type: string;
  scope: string;
  expires_in: number;
  access_token: string;
  id_token: string;
  refresh_token: string;
  user_name: string;
  email: string;
}

interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private http = inject(HttpClient);
  private tokenStorage = inject(TokenStorageService);
  private authConfig: AuthConfig | null = null;
  private authStateSubject = new BehaviorSubject<boolean>(
    this.isAuthenticated(),
  );
  public authState$ = this.authStateSubject.asObservable();

  public setAuthConfig(config: AuthConfig) {
    this.authConfig = config;
  }

  public getAuthConfig(): AuthConfig | null {
    return this.authConfig;
  }

  public getPostLoginRedirectUrl(): string {
    return this.authConfig?.postLoginRedirectUrl || '/dashboard';
  }

  private getApiAuthUrl(): string {
    if (!this.authConfig?.apiAuthUrl) {
      throw new Error('Auth configuration not set. Call setAuthConfig() first.');
    }
    return this.authConfig.apiAuthUrl;
  }

  private getRedirectUrl(): string {
    if (!this.authConfig?.redirectUrl) {
      throw new Error('Auth configuration not set. Call setAuthConfig() first.');
    }
    return this.authConfig.redirectUrl;
  }

  private getAuthHeaders(): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Basic YWRtaW46YWRtaW4xMjM=`,
    });
  }

  public getLoginFlow(): 'sso' | 'basic' {
    return this.tokenStorage.getLoginType() || 'sso';
  }

  loginSSO(redirectUrl?: string): Observable<LoginResponse> {
    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();

    console.log('AuthService: SSO login initiated for', finalRedirectUrl);
    const url = `${this.getApiAuthUrl()}/login-url?redirectUrl=${encodeURIComponent(finalRedirectUrl)}`;
    return this.http
      .get<LoginResponse>(url, { headers: this.getAuthHeaders() })
      .pipe(
        tap(({ loginUrl }) => {
          console.log('AuthService: Redirecting to SSO provider');
          window.location.href = loginUrl;
        }),
        catchError((error) => {
          console.error('AuthService: SSO login failed', error);
          return throwError(() => error);
        })
      );
  }

  exchangeCodeForToken(
    code: string,
    redirectUrl?: string,
  ): Observable<TokenPair> {
    const finalRedirectUrl = redirectUrl || this.getRedirectUrl();
    const encodedRedirectUrl = encodeURIComponent(finalRedirectUrl);
    const url = `${this.getApiAuthUrl()}/token?redirectUrl=${encodedRedirectUrl}`;
    console.log('AuthService: Token exchange initiated');

    return this.http
      .post<TokenResponse>(
        url,
        { code: code },
        { headers: this.getAuthHeaders() },
      )
      .pipe(
        tap((response) => {
          console.log('AuthService: Token exchange successful, storing tokens');
          this.tokenStorage.storeLoginType('sso');
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe({
              next: (defaultPath) => {
                if (defaultPath) {
                  console.log('AuthService: Default org path set during login', defaultPath);
                }
              },
              error: (error) => {
                console.error('AuthService: Failed to set default org path during login', error);
              }
            });
          }
        }),
        map((response) => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError((error) => {
          console.error('AuthService: Token exchange failed', error);
          return throwError(() => error);
        }),
      );
  }

  refreshToken(refreshToken?: string): Observable<TokenPair> {
    const refreshTokenFromStorage = this.tokenStorage.getRefreshToken();
    const url = `${this.getApiAuthUrl()}/refresh-token`;
    return this.http
      .post<TokenResponse>(
        url,
        { refreshToken: refreshToken ? refreshToken : refreshTokenFromStorage },
        { headers: this.getAuthHeaders() },
      )
      .pipe(
        tap((response) => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);

          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe({
              next: (defaultPath) => {
                if (defaultPath) {
                  console.log('AuthService: Default org path set during token refresh', defaultPath);
                }
              },
              error: (error) => {
                console.error('AuthService: Failed to set default org path during token refresh', error);
              }
            });
          }
        }),
        map((response) => ({
          accessToken: response.access_token,
          refreshToken: response.refresh_token,
        })),
        catchError((error) => {
          console.error('AuthService: Token refresh failed', error);
          return throwError(() => error);
        })
      );
  }

  logout(redirectUrl?: string): Observable<any> {
    const idToken = this.tokenStorage.getIdToken();
    
    // For logout, redirect to root (marketing app home)
    const finalRedirectUrl = redirectUrl || `${window.location.origin}/`;
    
    const url = `${this.getApiAuthUrl()}/logout-url?redirectUrl=${encodeURIComponent(finalRedirectUrl)}`;
    const headers = this.getAuthHeaders().set('X-ID-TOKEN', idToken || '');
    
    console.log('AuthService logout called with:', {
      providedRedirectUrl: redirectUrl,
      finalRedirectUrl,
      url
    });
    
    return this.http.get<any>(url, { headers }).pipe(
      tap(({ logoutUrl }) => {
        console.log('AuthService received logout URL from backend:', logoutUrl);
        this.tokenStorage.clearTokens();
        this.authStateSubject.next(false);
        console.log('AuthService clearing tokens and redirecting to logout URL...');
        window.location.href = logoutUrl;
      }),
      catchError((error) => {
        console.error('AuthService logout failed:', error);
        return throwError(() => error);
      }),
    );
  }

  isAuthenticated(): boolean {
    return !!this.tokenStorage.getAccessToken();
  }

  basicLoginWithCredentials(
    username: string,
    password: string,
  ): Observable<TokenResponse> {
    const url = `${this.getApiAuthUrl()}/basic/login`;
    const loginBody = {
      userName: username,
      password: password,
    };
    return this.http
      .post<TokenResponse>(url, loginBody, { headers: this.getAuthHeaders() })
      .pipe(
        tap((response) => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
          
          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe({
              next: (defaultPath) => {
                if (defaultPath) {
                  console.log('Default org path set during basic login:', defaultPath);
                }
              },
              error: (error) => {
                console.error('Failed to set default org path during basic login:', error);
              }
            });
          }
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('Basic login failed:', error);
          return throwError(() => error);
        }),
      );
  }

  basicRefreshToken(): Observable<TokenResponse> {
    const refreshToken = this.tokenStorage.getRefreshToken();
    const url = `${this.getApiAuthUrl()}/basic/refresh/token`;
    return this.http
      .post<TokenResponse>(
        url,
        { refreshToken },
        { headers: this.getAuthHeaders() },
      )
      .pipe(
        tap((response) => {
          const {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: expiresIn,
            user_name: da_name,
            email: da_username,
            id_token: idToken,
          } = response;
          this.tokenStorage.storeTokens(accessToken, refreshToken, expiresIn);
          this.tokenStorage.storeDaInfo(da_name, da_username, idToken);
          this.authStateSubject.next(true);
          
          // Set default org_path if not exists
          if (!this.tokenStorage.getCookie('org_path')) {
            this.setDefaultOrgPath().subscribe({
              next: (defaultPath) => {
                if (defaultPath) {
                  console.log('Default org path set during basic token refresh:', defaultPath);
                }
              },
              error: (error) => {
                console.error('Failed to set default org path during basic token refresh:', error);
              }
            });
          }
        }),
        catchError((error) => {
          console.error('Basic token refresh failed:', error);
          return throwError(() => error);
        }),
      );
  }
  
  private clearAuthState(): void {
    this.tokenStorage.clearTokens();
    this.authStateSubject.next(false);
  }

  basicLogout(): Observable<void> {
    const refreshToken = this.tokenStorage.getRefreshToken();
    if (!refreshToken) {
      this.clearAuthState();
      return of(void 0);
    }
    const url = `${this.getApiAuthUrl()}/basic/logout`;
    const headers = this.getAuthHeaders().set('X-REFRESH-TOKEN', refreshToken);
    return this.http
      .post(url, null, {
        headers,
        responseType: 'text',
      })
      .pipe(
        map((response) => {
          const normalizedResponse = response.trim();
          this.clearAuthState();
          if (normalizedResponse !== 'Logout successful.') {
            console.warn('Unexpected logout response:', response);
          }
          return void 0;
        }),
        catchError((error) => {
          this.clearAuthState();
          console.error('Basic logout failed:', error);
          return throwError(() => error);
        }),
      );
  }

  // Organization hierarchy methods
  public getOrganizationHierarchy(): Observable<Organization[]> {
    const url = `${this.getApiAuthUrl()}/organization/hierarchy`;
    return this.http.get<Organization[]>(url, { headers: this.getAuthHeaders() });
  }

  public setDefaultOrgPath(): Observable<string | null> {
    return this.getOrganizationHierarchy().pipe(
      map((hierarchyData) => {
        const defaultPath = this.findDefaultOrgPath(hierarchyData);
        if (defaultPath) {
          this.tokenStorage.setCookie('org_path', defaultPath);
          console.log('Default org path set:', defaultPath);
          return defaultPath;
        } else {
          console.log('No default org path found');
          return null;
        }
      }),
      catchError((error) => {
        console.error('Failed to load hierarchy:', error);
        return of(null);
      })
    );
  }

  private findDefaultOrgPath(hierarchyData: Organization[]): string | null {
    for (const org of hierarchyData) {
      for (const domain of org.domains) {
        for (const project of domain.projects) {
          const defaultTeam = project.teams.find((team) => team.default === true);
          if (defaultTeam) {
            // Format: orgName@domainName@projectName@teamName::orgId@domainId@projectId@teamId
            const orgName = org.organizationName;
            const domainName = domain.domainName;
            const projectName = project.projectName;
            const teamName = defaultTeam.teamName;
            
            return `${orgName}@${domainName}@${projectName}@${teamName}::${org.orgId}@${domain.domainId}@${project.projectId}@${defaultTeam.teamId}`;
          }
        }
      }
    }
    return null;
  }
}
