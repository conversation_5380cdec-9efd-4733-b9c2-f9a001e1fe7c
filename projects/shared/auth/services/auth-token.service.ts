import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from './auth.service';
import { HttpErrorResponse } from '@angular/common/http';
import { TokenStorageService } from './token-storage.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

@Injectable({
  providedIn: 'root',
})
export class AuthTokenService {
  private tokenCheckSubscription: Subscription | null = null;
  private tokenCheckInterval: any;
  private checkIntervalMs = 5000;
  private subscription = new Subscription();
  
  // Enhanced error handling and retry logic
  private refreshRetryCount = 0;
  private maxRefreshRetries = 2;
  private refreshRetryDelay = 2000; // 2 seconds
  private isRefreshing = false;
  private refreshQueue: Array<() => void> = [];

  constructor(
    private router: Router,
    private authService: AuthService,
    private tokenStorageService: TokenStorageService,
    private centralizedRedirectService: CentralizedRedirectService,
  ) {}

  /**
   * Handle post-login redirect using centralized redirect service
   */
  private handlePostLoginRedirect(): void {
    this.centralizedRedirectService.handlePostLoginRedirect();
  }

  public handleAuthCodeAndToken(): void {
    const authCode = new URLSearchParams(window.location.search).get('code');
    const accessToken = this.tokenStorageService.getAccessToken();
    const refreshToken = this.tokenStorageService.getRefreshToken();
    const loginType = this.tokenStorageService.getLoginType();

    console.log('AuthTokenService handling auth code and token:', {
      hasAuthCode: !!authCode,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      loginType,
      currentUrl: this.router.url
    });

    // If we have an authorization code, process it directly
    if (authCode) {
      console.log('Found authorization code, processing token exchange...');
      this.authService.exchangeCodeForToken(authCode).subscribe({
        next: (tokenPair) => {
          console.log('Token exchange successful:', tokenPair);
          // After successful token exchange, handle the redirect
          this.handlePostLoginRedirect();
        },
        error: (error) => {
          console.error('Token exchange failed:', error);
          // Redirect to login with error
          this.router.navigate(['/login'], {
            state: {
              error: 'Failed to complete authentication. Please try again.',
              errorType: 'sso_error'
            }
          });
        }
      });
      return;
    }

    // IMPORTANT: Don't store the current URL as intended destination if it contains auth code
    if (this.router.url.includes('code=') || this.router.url.includes('token=')) {
      console.log('Current URL contains auth code or token, not storing as intended destination');
      return;
    }

    // IMPORTANT: Don't store the current URL as intended destination if it contains URL-encoded auth code
    if (this.router.url.includes('%2F%3Fcode%3D') || this.router.url.includes('%2F%3Ftoken%3D')) {
      console.log('Current URL contains URL-encoded auth code or token, not storing as intended destination');
      return;
    }

    if (loginType === 'basic') {
      this.handleBasicLoginFlow(accessToken, refreshToken);
    } else {
      this.handleSSOFlow(accessToken, refreshToken, authCode);
    }
  }

  private handleBasicLoginFlow(
    accessToken: string | null,
    refreshToken: string | null,
  ): void {
    if (!accessToken && refreshToken) {
      this.handleBasicTokenRefresh();
    } else if (!accessToken && !refreshToken) {
      this.redirectToMarketingLogin();
    }
  }

  private handleSSOFlow(
    accessToken: string | null,
    refreshToken: string | null,
    authCode: string | null,
  ): void {
    if (!accessToken && refreshToken) {
      console.log('SSO flow: No access token but has refresh token, attempting token refresh');
      this.handleTokenRefresh(refreshToken);
    } else if (!accessToken && !refreshToken && authCode) {
      console.log('SSO flow: No tokens but has auth code, processing token exchange');
      this.authService.exchangeCodeForToken(authCode).subscribe({
        next: (tokenPair) => {
          console.log('Token exchange successful:', tokenPair);
          this.handlePostLoginRedirect();
        },
        error: (error) => {
          console.error('Token exchange failed:', error);
          this.redirectToMarketingLogin();
        }
      });
    } else if (!accessToken && !refreshToken && !authCode) {
      console.log('SSO flow: No tokens and no auth code, redirecting to marketing login');
      this.redirectToMarketingLogin();
    }
  }

  public startTokenCheck(): void {
    this.tokenCheckInterval = setInterval(() => {
      const accessToken = this.tokenStorageService.getAccessToken();
      const refreshToken = this.tokenStorageService.getRefreshToken();
      const loginType = this.tokenStorageService.getLoginType();

      // Check token expiry first
      this.checkTokenExpiry();

      // Check if user has access token and is on login page - show alert
      if (accessToken && this.router.url === '/login') {
        alert('Please refresh this page to ensure proper authentication state.');
      }

      // Only redirect to login if we have no tokens at all and we're not already on login/marketplace pages
      if (!accessToken && !refreshToken) {
        const currentUrl = this.router.url;
        if (!currentUrl.includes('/login') && !currentUrl.includes('/marketplace')) {
          console.info('No tokens found, redirecting to marketing login');
          this.tokenStorageService.clearTokens();
          this.redirectToMarketingLogin();
        }
      } else if (!accessToken && refreshToken) {
        // Try to refresh the token
        console.info('Access token missing, attempting refresh');
        if (this.shouldAttemptRefresh()) {
          if (loginType === 'basic') {
            this.handleBasicTokenRefresh();
          } else {
            this.handleTokenRefresh(refreshToken);
          }
        } else {
          console.log('Cannot attempt refresh, redirecting to login');
          this.redirectToMarketingLogin();
        }
      }
    }, this.checkIntervalMs);
  }

  /**
   * Check if the access token is about to expire (within 5 minutes)
   * If so, delete the current token and attempt to refresh
   */
  public checkTokenExpiry(): void {
    const expiry = this.tokenStorageService.getTokenExpiryTime();
    if (expiry) {
      const expiryTime = new Date(expiry).getTime();
      const currentTime = new Date().getTime();

      const diffInMs = expiryTime - currentTime;
      const diffInMinutes = diffInMs / (1000 * 60);
      
      // console.log('Token expiry check:', {
      //   expiryTime: new Date(expiry).toISOString(),
      //   currentTime: new Date(currentTime).toISOString(),
      //   diffInMinutes: diffInMinutes.toFixed(2),
      //   willExpireSoon: diffInMinutes <= 5 && diffInMinutes > 0
      // });

      if (diffInMinutes <= 5 && diffInMinutes > 0) {
        console.log('Token will expire within 5 minutes, deleting current token and attempting refresh');
        this.tokenStorageService.deleteCookie('access_token');
        
        // Determine login type and handle refresh accordingly
        const loginType = this.tokenStorageService.getLoginType();
        const refreshToken = this.tokenStorageService.getRefreshToken();
        
        if (this.shouldAttemptRefresh()) {
          if (loginType === 'basic') {
            this.handleBasicTokenRefresh();
          } else if (loginType === 'sso' && refreshToken) {
            this.handleTokenRefresh(refreshToken);
          } else {
            console.log('No valid refresh method available, redirecting to login');
            this.redirectToMarketingLogin();
          }
        } else {
          console.log('Cannot attempt refresh, redirecting to login');
          this.redirectToMarketingLogin();
        }
      }
    }
  }

  private handleBasicTokenRefresh(): void {
    if (this.isRefreshing) {
      // If already refreshing, queue this request
      this.refreshQueue.push(() => this.handleBasicTokenRefresh());
      return;
    }

    this.isRefreshing = true;
    console.log(`Attempting basic token refresh (attempt ${this.refreshRetryCount + 1}/${this.maxRefreshRetries + 1})`);

    const refreshSub = this.authService.basicRefreshToken().subscribe({
      next: (response) => {
        console.info('Basic token refreshed successfully');
        this.onRefreshSuccess();
      },
      error: (err: HttpErrorResponse) => {
        console.error('Basic token refresh failed:', err);
        this.handleRefreshError(err, 'basic');
      },
    });
    this.subscription.add(refreshSub);
  }

  private handleTokenRefresh(refreshToken: string): void {
    if (this.isRefreshing) {
      // If already refreshing, queue this request
      this.refreshQueue.push(() => this.handleTokenRefresh(refreshToken));
      return;
    }

    this.isRefreshing = true;
    console.log(`Attempting SSO token refresh (attempt ${this.refreshRetryCount + 1}/${this.maxRefreshRetries + 1})`);

    const refreshSub = this.authService.refreshToken(refreshToken).subscribe({
      next: (response) => {
        console.info('SSO token refreshed successfully');
        this.onRefreshSuccess();
      },
      error: (err: HttpErrorResponse) => {
        console.error('SSO token refresh failed:', err);
        this.handleRefreshError(err, 'sso');
      },
    });
    this.subscription.add(refreshSub);
  }

  /**
   * Handle successful token refresh
   */
  private onRefreshSuccess(): void {
    console.log('Token refresh successful, processing queue...');
    this.isRefreshing = false;
    this.refreshRetryCount = 0;
    
    // Process any queued refresh requests
    while (this.refreshQueue.length > 0) {
      const queuedRefresh = this.refreshQueue.shift();
      if (queuedRefresh) {
        queuedRefresh();
      }
    }
  }

  /**
   * Handle token refresh errors with retry logic and fallback strategies
   */
  private handleRefreshError(err: HttpErrorResponse, loginType: 'basic' | 'sso'): void {
    console.error(`Token refresh error (${loginType}):`, {
      status: err.status,
      statusText: err.statusText,
      error: err.error,
      retryCount: this.refreshRetryCount
    });

    this.isRefreshing = false;

    // Handle different error scenarios
    switch (err.status) {
      case 401: // Unauthorized - refresh token is invalid
        console.log('Refresh token is invalid (401), clearing tokens and redirecting to login');
        this.tokenStorageService.clearTokens();
        this.redirectToMarketingLogin();
        break;

      case 403: // Forbidden - refresh token is expired or revoked
        console.log('Refresh token is forbidden (403), clearing tokens and redirecting to login');
        this.tokenStorageService.clearTokens();
        this.redirectToMarketingLogin();
        break;

      case 429: // Too Many Requests - rate limiting
        console.log('Rate limited (429), will retry after delay');
        this.handleRateLimitError(loginType);
        break;

      case 500: // Server Error
      case 502: // Bad Gateway
      case 503: // Service Unavailable
      case 504: // Gateway Timeout
        console.log(`Server error (${err.status}), attempting retry`);
        this.handleServerError(loginType);
        break;

      case 0: // Network Error
      case -1: // Network Error
        console.log('Network error, attempting retry');
        this.handleNetworkError(loginType);
        break;

      default:
        console.log(`Unknown error (${err.status}), attempting retry`);
        this.handleGenericError(loginType);
        break;
    }
  }

  /**
   * Handle rate limiting errors
   */
  private handleRateLimitError(loginType: 'basic' | 'sso'): void {
    if (this.refreshRetryCount < this.maxRefreshRetries) {
      this.refreshRetryCount++;
      console.log(`Rate limited, retrying in ${this.refreshRetryDelay * 2}ms (attempt ${this.refreshRetryCount}/${this.maxRefreshRetries})`);
      
      setTimeout(() => {
        if (loginType === 'basic') {
          this.handleBasicTokenRefresh();
        } else {
          const refreshToken = this.tokenStorageService.getRefreshToken();
          if (refreshToken) {
            this.handleTokenRefresh(refreshToken);
          }
        }
      }, this.refreshRetryDelay * 2); // Longer delay for rate limiting
    } else {
      console.log('Max retries reached for rate limiting, redirecting to login');
      this.tokenStorageService.clearTokens();
      this.redirectToMarketingLogin();
    }
  }

  /**
   * Handle server errors with exponential backoff
   */
  private handleServerError(loginType: 'basic' | 'sso'): void {
    if (this.refreshRetryCount < this.maxRefreshRetries) {
      this.refreshRetryCount++;
      const delay = this.refreshRetryDelay * Math.pow(2, this.refreshRetryCount - 1); // Exponential backoff
      console.log(`Server error, retrying in ${delay}ms (attempt ${this.refreshRetryCount}/${this.maxRefreshRetries})`);
      
      setTimeout(() => {
        if (loginType === 'basic') {
          this.handleBasicTokenRefresh();
        } else {
          const refreshToken = this.tokenStorageService.getRefreshToken();
          if (refreshToken) {
            this.handleTokenRefresh(refreshToken);
          }
        }
      }, delay);
    } else {
      console.log('Max retries reached for server errors, redirecting to login');
      this.tokenStorageService.clearTokens();
      this.redirectToMarketingLogin();
    }
  }

  /**
   * Handle network errors
   */
  private handleNetworkError(loginType: 'basic' | 'sso'): void {
    if (this.refreshRetryCount < this.maxRefreshRetries) {
      this.refreshRetryCount++;
      console.log(`Network error, retrying in ${this.refreshRetryDelay}ms (attempt ${this.refreshRetryCount}/${this.maxRefreshRetries})`);
      
      setTimeout(() => {
        if (loginType === 'basic') {
          this.handleBasicTokenRefresh();
        } else {
          const refreshToken = this.tokenStorageService.getRefreshToken();
          if (refreshToken) {
            this.handleTokenRefresh(refreshToken);
          }
        }
      }, this.refreshRetryDelay);
    } else {
      console.log('Max retries reached for network errors, redirecting to login');
      this.tokenStorageService.clearTokens();
      this.redirectToMarketingLogin();
    }
  }

  /**
   * Handle generic errors
   */
  private handleGenericError(loginType: 'basic' | 'sso'): void {
    if (this.refreshRetryCount < this.maxRefreshRetries) {
      this.refreshRetryCount++;
      console.log(`Generic error, retrying in ${this.refreshRetryDelay}ms (attempt ${this.refreshRetryCount}/${this.maxRefreshRetries})`);
      
      setTimeout(() => {
        if (loginType === 'basic') {
          this.handleBasicTokenRefresh();
        } else {
          const refreshToken = this.tokenStorageService.getRefreshToken();
          if (refreshToken) {
            this.handleTokenRefresh(refreshToken);
          }
        }
      }, this.refreshRetryDelay);
    } else {
      console.log('Max retries reached for generic errors, redirecting to login');
      this.tokenStorageService.clearTokens();
      this.redirectToMarketingLogin();
    }
  }

  /**
   * Check if we should attempt a refresh based on current state
   */
  private shouldAttemptRefresh(): boolean {
    const refreshToken = this.tokenStorageService.getRefreshToken();
    const loginType = this.tokenStorageService.getLoginType();
    
    if (!refreshToken) {
      console.log('No refresh token available, cannot attempt refresh');
      return false;
    }

    if (this.isRefreshing) {
      console.log('Refresh already in progress, skipping');
      return false;
    }

    if (this.refreshRetryCount >= this.maxRefreshRetries) {
      console.log('Max retries reached, not attempting refresh');
      return false;
    }

    return true;
  }

  private redirectToMarketingLogin(): void {
    // Store current URL and redirect to marketing login
    this.centralizedRedirectService.storeIntendedDestination(window.location.href);
    window.location.href = `${window.location.origin}/login`;
  }

  public stopTokenCheck(): void {
    if (this.tokenCheckInterval) {
      clearInterval(this.tokenCheckInterval);
      this.tokenCheckInterval = null;
    }
    this.subscription.unsubscribe();
  }
} 