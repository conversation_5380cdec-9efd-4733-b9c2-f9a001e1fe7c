import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TokenStorageService {
  private ACCESS_TOKEN_KEY = 'access_token';
  private REFRESH_TOKEN_KEY = 'refresh_token';
  private DA_NAME_KEY = 'da_name';
  private DA_USERNAME_KEY = 'da_username';
  private DA_ID_TOKEN = 'id_token';
  private LOGIN_TYPE_KEY = 'login_type';
  private ACCESS_TOKEN_EXPIRES_KEY = 'access_token_expires';

  private getCurrentDomain(): string {
    const hostname = window.location.hostname;
    const port = window.location.port;
    
    // For Docker setup: all apps run on localhost without ports
    if (hostname === 'localhost' && (port === '' || port === '80' || port === '443')) {
      return '';
    }
    
    // For traditional local development with ports
    if (hostname === 'localhost' && ['4200', '4201', '4202', '4203', '4204'].includes(port)) {
      return '';
    }
    
    // For production, use the hostname
    return hostname;
  }

  public storeLoginType(loginType: 'sso' | 'basic'): void {
    this.setCookie(this.LOGIN_TYPE_KEY, loginType);
  }

  public getLoginType(): 'sso' | 'basic' | null {
    const loginType = this.getCookie(this.LOGIN_TYPE_KEY);
    return loginType === 'sso' || loginType === 'basic' ? loginType : null;
  }

  public setCookie(cname: string, cvalue: string, exseconds?: number): void {
    const d = new Date();
    let expires = "";
    if (exseconds) {
      d.setTime(d.getTime() + (exseconds * 1000));
      expires = "expires=" + d.toUTCString();
    }
    
    let secureFlag = window.location.protocol === "https:" ? ";Secure" : "";
    let sameSiteFlag = ";SameSite=Lax"; // Changed from Strict to Lax for cross-app compatibility
    let domainFlag = "";
    
    const domain = this.getCurrentDomain();
    if (domain) {
      domainFlag = `;domain=${domain}`;
    }
    
    document.cookie = `${cname}=${cvalue};${expires};path=/;${secureFlag}${sameSiteFlag}${domainFlag}`;
    // console.log(`TokenStorage: Cookie set: ${cname}=${cvalue.substring(0, 20)}... (domain: ${domain || 'localhost'})`);
  }

  public getCookie(name: string): string | null {
    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
    const value = match ? decodeURIComponent(match[2]) : null;
    
    // if (value) {
    //   console.log(`TokenStorage: Retrieved cookie: ${name}=${value.substring(0, 20)}...`);
    // } else {
    //   console.log(`TokenStorage: Cookie not found: ${name}`);
    // }
    
    return value;
  }

  public deleteCookie(name: string): void {
    const domain = this.getCurrentDomain();
    let domainFlag = "";

    if (domain) {
      domainFlag = `;domain=${domain}`;
    }
    
    document.cookie = `${name}=; path=/; max-age=0; SameSite=Lax${domainFlag}`;
    console.log(`TokenStorage: Cookie deleted: ${name} (domain: ${domain || 'localhost'})`);
    
    // If deleting access token, also clear the expiry time
    if (name === this.ACCESS_TOKEN_KEY) {
      this.deleteCookie(this.ACCESS_TOKEN_EXPIRES_KEY);
      console.log('TokenStorage: Access token expiry time cleared from cookies');
    }
  }

  storeTokens(accessToken: string, refreshToken: string, expiresInSeconds: number): void {
    console.log('TokenStorage: Storing tokens...', {
      accessTokenLength: accessToken.length,
      refreshTokenLength: refreshToken.length,
      expiresInSeconds
    });
    
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
    this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken);

    // Store expiry time in cookies for consistency
    const expiryTime = new Date();
    expiryTime.setSeconds(expiryTime.getSeconds() + expiresInSeconds);
    this.setCookie(this.ACCESS_TOKEN_EXPIRES_KEY, expiryTime.toISOString());

    console.log('TokenStorage: Tokens stored successfully');
  }

  storeAccessToken(accessToken: string, expiresInSeconds?: number): void {
    this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, expiresInSeconds);
  }

  storeDaInfo(daName: string, daUsername: string, idToken: string): void {
    console.log('TokenStorage: Storing user info...', {
      daName,
      daUsername,
      idTokenLength: idToken.length
    });
    
    this.setCookie(this.DA_NAME_KEY, daName);
    this.setCookie(this.DA_USERNAME_KEY, daUsername);
    this.setCookie(this.DA_ID_TOKEN, idToken);
    
    console.log('TokenStorage: User info stored successfully');
  }

  getAccessToken(): string | null {
    return this.getCookie(this.ACCESS_TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return this.getCookie(this.REFRESH_TOKEN_KEY);
  }

  getDaName(): string | null {
    return this.getCookie(this.DA_NAME_KEY);
  }

  getDaUsername(): string | null {
    return this.getCookie(this.DA_USERNAME_KEY);
  }

  getIdToken(): string | null {
    return this.getCookie(this.DA_ID_TOKEN);
  }

  getTokenExpiryTime(): string | null {
    return this.getCookie(this.ACCESS_TOKEN_EXPIRES_KEY);
  }

  clearTokens(): void {
    this.deleteCookie(this.ACCESS_TOKEN_KEY);
    this.deleteCookie(this.REFRESH_TOKEN_KEY);
    this.deleteCookie(this.DA_NAME_KEY);
    this.deleteCookie(this.DA_USERNAME_KEY);
    this.deleteCookie(this.DA_ID_TOKEN);
    this.deleteCookie(this.LOGIN_TYPE_KEY);
    this.deleteCookie(this.ACCESS_TOKEN_EXPIRES_KEY);
  }
} 