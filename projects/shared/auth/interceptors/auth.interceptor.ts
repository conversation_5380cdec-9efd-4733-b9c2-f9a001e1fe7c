import { Injectable, inject } from '@angular/core';
import {
  <PERSON>ttp<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { TokenStorageService } from '../services/token-storage.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private tokenStorageService = inject(TokenStorageService);
  private authService = inject(AuthService);
  private isRefreshing = false;
  private refreshTokenSubject = new BehaviorSubject<string | null>(null);

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    // If the request already has an Authorization header (e.g., set by AuthService.getAuthHeaders for Basic auth),
    // do not add/override it with Bearer. Pass through unchanged.
    if (req.headers.has('Authorization')) {
      return next
        .handle(req)
        .pipe(catchError((error) => this.handleError(error, req, next)));
    }

    const accessToken = this.tokenStorageService.getAccessToken();
    
    // Add token header if token exists
    const authReq = accessToken ? this.addTokenHeader(req, accessToken) : req;
      
    return next
      .handle(authReq)
      .pipe(catchError((error) => this.handleError(error, authReq, next)));
  }

 private addTokenHeader(
    request: HttpRequest<any>,
    token: string,
  ): HttpRequest<any> {
    // Add token header using Authorization Bearer format for JWT
    const headers = request.headers.set('Authorization', `Bearer ${token.trim()}`);
    
    const result = request.clone({
      headers
    });
    
    return result;
  }

  private handleError(
    error: any,
    request: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    if (error instanceof HttpErrorResponse && error.status === 401) {
      return this.handle401Error(request, next);
    }
    return throwError(() => error);
  }

  private handle401Error(
    request: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      // Determine login type and use appropriate refresh method
      const loginType = this.tokenStorageService.getLoginType();

      if (loginType === 'basic') {
        return this.authService.basicRefreshToken().pipe(
          switchMap((tokenResponse: any) => {
            this.isRefreshing = false;
            const newAccessToken = tokenResponse.access_token;
            this.tokenStorageService.storeAccessToken(newAccessToken);
            this.refreshTokenSubject.next(newAccessToken);
            return next.handle(this.addTokenHeader(request, newAccessToken));
          }),
          catchError((err) => {
            this.isRefreshing = false;
            return throwError(() => err);
          }),
        );
      } else {
        return this.authService.refreshToken().pipe(
          switchMap((tokenResponse: any) => {
            this.isRefreshing = false;
            const newAccessToken = tokenResponse.accessToken;
            this.tokenStorageService.storeAccessToken(newAccessToken);
            this.refreshTokenSubject.next(newAccessToken);
            return next.handle(this.addTokenHeader(request, newAccessToken));
          }),
          catchError((err) => {
            this.isRefreshing = false;
            return throwError(() => err);
          }),
        );
      }
    }
    return this.refreshTokenSubject.pipe(
      filter((token) => token != null),
      take(1),
      switchMap((token) => {
        return next.handle(this.addTokenHeader(request, token!));
      }),
    );
  }
} 