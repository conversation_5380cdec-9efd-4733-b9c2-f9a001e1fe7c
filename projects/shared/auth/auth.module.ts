import { NgModule, Provider } from '@angular/core';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { AuthService } from './services/auth.service';
import { AuthStateService } from './services/auth-state.service';
import { AuthTokenService } from './services/auth-token.service';
import { TokenStorageService } from './services/token-storage.service';
import { AuthGuard } from './guards/auth.guard';
import { AuthInterceptor } from './interceptors/auth.interceptor';

const AUTH_PROVIDERS: Provider[] = [
  AuthService,
  AuthStateService,
  AuthTokenService,
  TokenStorageService,
  AuthGuard,
  { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
];

@NgModule({
  imports: [CommonModule],
  providers: AUTH_PROVIDERS,
})
export class AuthModule {}